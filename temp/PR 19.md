
**1. `SessionListContract.kt` (New File)**

```kotlin
// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/SessionListContract.kt
package eu.torvian.chatbot.app.viewmodel

import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.common.models.ChatGroup
import eu.torvian.chatbot.common.models.ChatSessionSummary

/**
 * Encapsulates all UI state relevant to the Session List Panel.
 */
data class SessionListUiState(
    val uiState: UiState<ApiError, SessionListViewModel.SessionListData>,
    val selectedSessionId: Long?,
    val isCreatingNewGroup: Boolean,
    val newGroupNameInput: String,
    val editingGroup: ChatGroup?,
    val editingGroupNameInput: String
)

/**
 * Defines all UI actions that can be triggered from the Session List Panel.
 */
interface SessionListActions {
    fun onSessionSelected(sessionId: Long)
    fun onCreateNewSession(name: String?)
    fun onRenameSession(session: ChatSessionSummary, newName: String)
    fun onDeleteSession(sessionId: Long)
    fun onAssignSessionToGroup(sessionId: Long, groupId: Long?)
    fun onStartCreatingNewGroup()
    fun onUpdateNewGroupNameInput(newText: String)
    fun onCreateNewGroup()
    fun onCancelCreatingNewGroup()
    fun onStartRenamingGroup(group: ChatGroup)
    fun onUpdateEditingGroupNameInput(newText: String)
    fun onSaveRenamedGroup()
    fun onCancelRenamingGroup()
    fun onDeleteGroup(groupId: Long)
}
```

**2. `ChatAreaContract.kt` (New File)**

```kotlin
// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/ChatAreaContract.kt
package eu.torvian.chatbot.app.viewmodel

import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.ChatSession

/**
 * Encapsulates all UI state relevant to the main Chat Area.
 * (To be fully implemented in future PRs like 20, 21, etc.)
 */
data class ChatAreaUiState(
    val sessionState: UiState<ApiError, ChatSession>,
    val currentBranchLeafId: Long?,
    val displayedMessages: List<ChatMessage>,
    val inputContent: String,
    val replyTargetMessage: ChatMessage?,
    val editingMessage: ChatMessage?,
    val editingContent: String
    // Will include model/settings selection states from ChatViewModel in future PRs
)

/**
 * Defines all UI actions that can be triggered from the main Chat Area.
 * (To be fully implemented in future PRs like 20, 21, etc.)
 */
interface ChatAreaActions {
    fun onUpdateInput(newText: String)
    fun onSendMessage()
    fun onStartReplyTo(message: ChatMessage)
    fun onCancelReply()
    fun onStartEditing(message: ChatMessage)
    fun onUpdateEditingContent(newText: String)
    fun onSaveEditing()
    fun onCancelEditing()
    fun onDeleteMessage(messageId: Long)
    fun onSwitchBranchToMessage(messageId: Long)
    fun onSelectModel(modelId: Long?)
    fun onSelectSettings(settingsId: Long?)
    // Will include copy actions (E2.S7, E3.S5) in future PRs
}
```

**3. `ChatScreen.kt` (Updated - Stateful Screen, orchestrates contracts)**

```kotlin
// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/ChatScreen.kt
package eu.torvian.chatbot.app.compose

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import eu.torvian.chatbot.app.viewmodel.ChatAreaActions
import eu.torvian.chatbot.app.viewmodel.ChatAreaUiState
import eu.torvian.chatbot.app.viewmodel.ChatViewModel
import eu.torvian.chatbot.app.viewmodel.SessionListActions
import eu.torvian.chatbot.app.viewmodel.SessionListUiState
import eu.torvian.chatbot.app.viewmodel.SessionListViewModel
import eu.torvian.chatbot.common.models.ChatGroup
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.ChatSessionSummary
import org.koin.compose.viewmodel.koinViewModel

/**
 * A stateful wrapper Composable for the main chat interface.
 * This component is responsible for:
 * - Obtaining ViewModels via Koin.
 * - Collecting necessary state from these ViewModels.
 * - Managing specific ViewModel interactions for the chat feature.
 * - Constructing and passing stateless UI state and action contracts to [ChatScreenContent].
 * (E7.S2: Implementing the Stateful part of the Screen with internal ViewModel management)
 */
@Composable
fun ChatScreen(
    sessionListViewModel: SessionListViewModel = koinViewModel(),
    chatViewModel: ChatViewModel = koinViewModel()
) {
    // --- Collect States for SessionListPanel ---
    val sessionListUiStateState by sessionListViewModel.listState.collectAsState() // Renamed to avoid conflict
    val selectedSessionId by sessionListViewModel.selectedSessionId.collectAsState()
    val isCreatingNewGroup by sessionListViewModel.isCreatingNewGroup.collectAsState()
    val newGroupNameInput by sessionListViewModel.newGroupNameInput.collectAsState()
    val editingGroup by sessionListViewModel.editingGroup.collectAsState()
    val editingGroupNameInput by sessionListViewModel.editingGroupNameInput.collectAsState()

    // --- Collect States for ChatArea ---
    val chatSessionState by chatViewModel.sessionState.collectAsState() // Renamed for clarity
    val chatInputContent by chatViewModel.inputContent.collectAsState()
    val chatReplyTargetMessage by chatViewModel.replyTargetMessage.collectAsState()
    val chatEditingMessage by chatViewModel.editingMessage.collectAsState()
    val chatEditingContent by chatViewModel.editingContent.collectAsState()
    val chatCurrentBranchLeafId by chatViewModel.currentBranchLeafId.collectAsState()
    val chatDisplayedMessages by chatViewModel.displayedMessages.collectAsState()

    // Determine if the overall screen should show a loading overlay
    val showLoading = sessionListUiStateState.isLoading || chatSessionState.isLoading // Use chatSessionState

    // --- SessionListPanel Contract Construction ---
    val sessionListPanelUiState = remember(
        sessionListUiStateState, selectedSessionId, isCreatingNewGroup,
        newGroupNameInput, editingGroup, editingGroupNameInput
    ) {
        SessionListUiState(
            uiState = sessionListUiStateState,
            selectedSessionId = selectedSessionId,
            isCreatingNewGroup = isCreatingNewGroup,
            newGroupNameInput = newGroupNameInput,
            editingGroup = editingGroup,
            editingGroupNameInput = editingGroupNameInput
        )
    }
    val sessionListPanelActions = remember(sessionListViewModel) {
        object : SessionListActions {
            override fun onSessionSelected(sessionId: Long) = sessionListViewModel.selectSession(sessionId)
            override fun onCreateNewSession(name: String?) = sessionListViewModel.createNewSession(name)
            override fun onRenameSession(session: ChatSessionSummary, newName: String) = sessionListViewModel.renameSession(session, newName)
            override fun onDeleteSession(sessionId: Long) = sessionListViewModel.deleteSession(sessionId)
            override fun onAssignSessionToGroup(sessionId: Long, groupId: Long?) = sessionListViewModel.assignSessionToGroup(sessionId, groupId)
            override fun onStartCreatingNewGroup() = sessionListViewModel.startCreatingNewGroup()
            override fun onUpdateNewGroupNameInput(newText: String) = sessionListViewModel.updateNewGroupNameInput(newText)
            override fun onCreateNewGroup() = sessionListViewModel.createNewGroup()
            override fun onCancelCreatingNewGroup() = sessionListViewModel.cancelCreatingNewGroup()
            override fun onStartRenamingGroup(group: ChatGroup) = sessionListViewModel.startRenamingGroup(group)
            override fun onUpdateEditingGroupNameInput(newText: String) = sessionListViewModel.updateEditingGroupNameInput(newText)
            override fun onSaveRenamedGroup() = sessionListViewModel.saveRenamedGroup()
            override fun onCancelRenamingGroup() = sessionListViewModel.cancelRenamingGroup()
            override fun onDeleteGroup(groupId: Long) = sessionListViewModel.deleteGroup(groupId)
        }
    }

    // --- ChatArea Contract Construction ---
    val chatAreaUiState = remember(
        chatSessionState, chatInputContent, chatReplyTargetMessage,
        chatEditingMessage, chatEditingContent, chatCurrentBranchLeafId, chatDisplayedMessages
    ) {
        ChatAreaUiState(
            sessionState = chatSessionState,
            inputContent = chatInputContent,
            replyTargetMessage = chatReplyTargetMessage,
            editingMessage = chatEditingMessage,
            editingContent = chatEditingContent,
            currentBranchLeafId = chatCurrentBranchLeafId,
            displayedMessages = chatDisplayedMessages
        )
    }
    val chatAreaActions = remember(chatViewModel) {
        object : ChatAreaActions {
            override fun onUpdateInput(newText: String) = chatViewModel.updateInput(newText)
            override fun onSendMessage() = chatViewModel.sendMessage()
            override fun onStartReplyTo(message: ChatMessage) = chatViewModel.startReplyTo(message)
            override fun onCancelReply() = chatViewModel.cancelReply()
            override fun onStartEditing(message: ChatMessage) = chatViewModel.startEditing(message)
            override fun onUpdateEditingContent(newText: String) = chatViewModel.updateEditingContent(newText)
            override fun onSaveEditing() = chatViewModel.saveEditing()
            override fun onCancelEditing() = chatViewModel.cancelEditing()
            override fun onDeleteMessage(messageId: Long) = chatViewModel.deleteMessage(messageId)
            override fun onSwitchBranchToMessage(messageId: Long) = chatViewModel.switchBranchToMessage(messageId)
            override fun onSelectModel(modelId: Long?) = chatViewModel.selectModel(modelId)
            override fun onSelectSettings(settingsId: Long?) = chatViewModel.selectSettings(settingsId)
        }
    }

    // This interaction remains here as it links the selectedSessionId (from SessionListViewModel)
    // to the chatViewModel's loadSession logic.
    LaunchedEffect(selectedSessionId) {
        selectedSessionId?.let { sessionId ->
            chatViewModel.loadSession(sessionId)
        } ?: chatViewModel.clearSession()
    }

    // Pass all collected states and actions to the stateless ChatScreenContent
    ChatScreenContent(
        sessionListUiState = sessionListPanelUiState,
        sessionListActions = sessionListPanelActions,
        chatAreaUiState = chatAreaUiState,
        chatAreaActions = chatAreaActions,
        showLoading = showLoading
    )
}
```

**4. `ChatScreenContent.kt` (Stateless Layout for Chat Screen)**

```kotlin
// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/ChatScreenContent.kt
package eu.torvian.chatbot.app.compose

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import eu.torvian.chatbot.app.compose.common.LoadingOverlay
import eu.torvian.chatbot.app.viewmodel.ChatAreaActions
import eu.torvian.chatbot.app.viewmodel.ChatAreaUiState
import eu.torvian.chatbot.app.viewmodel.SessionListActions
import eu.torvian.chatbot.app.viewmodel.SessionListUiState

/**
 * Composable for the main chat interface's content, including the session list and the chat area.
 * This composable is now stateless, receiving all necessary data and callbacks via parameters.
 * (Part of E7.S2: Implement Base App Layout & ViewModel Integration - with State Hoisting)
 *
 * @param sessionListUiState The current UI state contract for the session list panel.
 * @param sessionListActions The actions contract for the session list panel.
 * @param chatAreaUiState The current UI state contract for the chat area.
 * @param chatAreaActions The actions contract for the chat area.
 * @param showLoading Whether to display the global loading overlay.
 */
@Composable
fun ChatScreenContent(
    sessionListUiState: SessionListUiState,
    sessionListActions: SessionListActions,
    chatAreaUiState: ChatAreaUiState,
    chatAreaActions: ChatAreaActions,
    showLoading: Boolean
) {
    // Wrap the Row in a Box to allow stacking the overlay on top
    Box(modifier = Modifier.fillMaxSize()) {
        Row(modifier = Modifier.fillMaxSize()) {
            Box(
                modifier = Modifier
                    .weight(0.25f) // Fixed weight for Session List Panel
                    .fillMaxSize()
                    .background(MaterialTheme.colorScheme.surface)
                    .padding(16.dp),
            ) {
                // PR 19: Session List Panel
                SessionListPanel(
                    uiState = sessionListUiState,
                    actions = sessionListActions
                )
            }
            Box(
                modifier = Modifier
                    .weight(0.75f) // Fixed weight for Chat Area
                    .fillMaxSize()
                    .background(MaterialTheme.colorScheme.background)
                    .padding(16.dp),
                contentAlignment = Alignment.Center
            ) {
                // PR 20 & 21 will fill this with actual Chat Area UI.
                // It will receive chat-specific states and actions via hoisted parameters.
                Text(
                    "Chat Area\n(PRs 20, 21, etc. will consume chatAreaUiState and chatAreaActions)\n" +
                            "Current chat state: ${chatAreaUiState.sessionState::class.simpleName}\n" +
                            "Current input: ${chatAreaUiState.inputContent.take(20)}...",
                    color = MaterialTheme.colorScheme.onBackground
                )
            }
        }
        // Display the loading overlay over the entire content of ChatScreenContent
        if (showLoading) {
            LoadingOverlay(modifier = Modifier.fillMaxSize())
        }
    }
}
```

**5. `SessionListPanel.kt` (Updated - Fully Stateless Component, uses contracts)**

```kotlin
// file: C:/Users/<USER>/Documents/MyData/MyProjects/Chatbot/chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/SessionListPanel.kt
package eu.torvian.chatbot.app.compose

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.PointerEventType
import androidx.compose.ui.input.pointer.onPointerEvent
import androidx.compose.ui.unit.dp
import eu.torvian.chatbot.app.compose.common.LoadingOverlay
import eu.torvian.chatbot.app.viewmodel.SessionListActions
import eu.torvian.chatbot.app.viewmodel.SessionListUiState
import eu.torvian.chatbot.app.viewmodel.UiState
import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.common.models.ChatGroup
import eu.torvian.chatbot.common.models.ChatSessionSummary

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun SessionListPanel(
    uiState: SessionListUiState,
    actions: SessionListActions
) {
    // Dialog states for sessions (internal UI state, not hoisted)
    var showNewSessionDialog by remember { mutableStateOf(false) }
    var newSessionNameInput by remember { mutableStateOf("") }
    var showRenameSessionDialog by remember { mutableStateOf(false) }
    var renameSessionNameInput by remember { mutableStateOf("") }
    var sessionToRename by remember { mutableStateOf<ChatSessionSummary?>(null) }
    var showDeleteSessionDialog by remember { mutableStateOf(false) }
    var sessionToDeleteId by remember { mutableStateOf<Long?>(null) }
    var showAssignGroupDialog by remember { mutableStateOf(false) }
    var sessionToAssign by remember { mutableStateOf<ChatSessionSummary?>(null) }

    // Dialog states for groups (internal UI state, not hoisted)
    var showDeleteGroupDialog by remember { mutableStateOf(false) }
    var groupToDeleteId by remember { mutableStateOf<Long?>(null) }


    Column(modifier = Modifier.fillMaxSize()) {
        // --- Header and New Session/Group Buttons ---
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                "Chat Sessions",
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.onSurface
            )
            Row {
                FilledIconButton(
                    onClick = { showNewSessionDialog = true },
                    modifier = Modifier.size(36.dp)
                ) {
                    Icon(Icons.Default.Add, contentDescription = "New Session")
                }
                Spacer(Modifier.width(8.dp))
                FilledIconButton(
                    onClick = actions::onStartCreatingNewGroup, // Use actions
                    modifier = Modifier.size(36.dp)
                ) {
                    Icon(Icons.Default.CreateNewFolder, contentDescription = "New Group")
                }
            }
        }

        // --- New Group Input (E6.S3) ---
        AnimatedVisibility(
            visible = uiState.isCreatingNewGroup, // Use uiState
            enter = expandVertically(),
            exit = shrinkVertically()
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OutlinedTextField(
                    value = uiState.newGroupNameInput, // Use uiState
                    onValueChange = actions::onUpdateNewGroupNameInput, // Use actions
                    label = { Text("New Group Name") },
                    singleLine = true,
                    modifier = Modifier.weight(1f)
                )
                Spacer(Modifier.width(8.dp))
                IconButton(onClick = actions::onCreateNewGroup) { // Use actions
                    Icon(Icons.Default.Check, contentDescription = "Create Group")
                }
                IconButton(onClick = actions::onCancelCreatingNewGroup) { // Use actions
                    Icon(Icons.Default.Close, contentDescription = "Cancel")
                }
            }
        }

        // --- Main Content: Session List (E2.S3, E6.S2) ---
        Box(modifier = Modifier.weight(1f).fillMaxWidth()) {
            when (val listState = uiState.uiState) { // Use uiState.uiState
                UiState.Loading -> LoadingOverlay(Modifier.fillMaxSize())
                is UiState.Error -> {
                    Text(
                        "Error loading sessions: ${listState.error.message}",
                        color = MaterialTheme.colorScheme.error,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                UiState.Idle -> {
                    Text(
                        "No sessions loaded. Click 'New Session' to start.",
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                is UiState.Success -> {
                    val sessionListData = listState.data // Data now from uiState.uiState.data
                    LazyColumn(modifier = Modifier.fillMaxSize()) {
                        sessionListData.groupedSessions.forEach { (group, sessions) ->
                            // Group Header (E6.S4)
                            stickyHeader {
                                GroupHeader(
                                    group = group,
                                    isEditing = uiState.editingGroup?.id == group?.id, // Use uiState
                                    editingName = uiState.editingGroupNameInput, // Use uiState
                                    onEditNameChange = actions::onUpdateEditingGroupNameInput, // Use actions
                                    onSaveRename = actions::onSaveRenamedGroup, // Use actions
                                    onCancelRename = actions::onCancelRenamingGroup, // Use actions
                                    onStartRename = actions::onStartRenamingGroup, // Use actions
                                    onDelete = { g ->
                                        groupToDeleteId = g?.id
                                        showDeleteGroupDialog = true
                                    }
                                )
                            }
                            // Sessions within the group
                            items(sessions, key = { it.id }) { session ->
                                SessionListItem(
                                    session = session,
                                    isSelected = session.id == uiState.selectedSessionId, // Use uiState
                                    onClick = { actions.onSessionSelected(session.id) }, // Use actions
                                    onRename = { s ->
                                        sessionToRename = s
                                        renameSessionNameInput = s.name
                                        showRenameSessionDialog = true
                                    },
                                    onDelete = { sId ->
                                        sessionToDeleteId = sId
                                        showDeleteSessionDialog = true
                                    },
                                    onAssignToGroup = { s ->
                                        sessionToAssign = s
                                        showAssignGroupDialog = true
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    // --- Dialogs ---
    // New Session Dialog (E2.S1)
    if (showNewSessionDialog) {
        AlertDialog(
            onDismissRequest = { showNewSessionDialog = false },
            title = { Text("Create New Chat Session") },
            text = {
                OutlinedTextField(
                    value = newSessionNameInput,
                    onValueChange = { newSessionNameInput = it },
                    label = { Text("Session Name (optional)") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )
            },
            confirmButton = {
                Button(
                    onClick = {
                        actions.onCreateNewSession(newSessionNameInput.ifBlank { null }) // Use actions
                        showNewSessionDialog = false
                        newSessionNameInput = ""
                    }
                ) { Text("Create") }
            },
            dismissButton = {
                TextButton(onClick = { showNewSessionDialog = false }) { Text("Cancel") }
            }
        )
    }

    // Rename Session Dialog (E2.S5)
    if (showRenameSessionDialog && sessionToRename != null) {
        AlertDialog(
            onDismissRequest = { showRenameSessionDialog = false; sessionToRename = null },
            title = { Text("Rename Session") },
            text = {
                OutlinedTextField(
                    value = renameSessionNameInput,
                    onValueChange = { renameSessionNameInput = it },
                    label = { Text("New Session Name") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )
            },
            confirmButton = {
                Button(
                    onClick = {
                        sessionToRename?.let { actions.onRenameSession(it, renameSessionNameInput) } // Use actions
                        showRenameSessionDialog = false
                        sessionToRename = null
                    },
                    enabled = renameSessionNameInput.isNotBlank()
                ) { Text("Rename") }
            },
            dismissButton = {
                TextButton(onClick = { showRenameSessionDialog = false; sessionToRename = null }) { Text("Cancel") }
            }
        )
    }

    // Delete Session Confirmation Dialog (E2.S6)
    if (showDeleteSessionDialog && sessionToDeleteId != null) {
        AlertDialog(
            onDismissRequest = { showDeleteSessionDialog = false; sessionToDeleteId = null },
            title = { Text("Delete Session?") },
            text = { Text("Are you sure you want to delete this session and all its messages? This action cannot be undone.") },
            confirmButton = {
                Button(
                    onClick = {
                        sessionToDeleteId?.let { actions.onDeleteSession(it) } // Use actions
                        showDeleteSessionDialog = false
                        sessionToDeleteId = null
                    },
                    colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.error)
                ) { Text("Delete") }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteSessionDialog = false; sessionToDeleteId = null }) { Text("Cancel") }
            }
        )
    }

    // Assign Session to Group Dialog (E6.S1)
    if (showAssignGroupDialog && sessionToAssign != null) {
        val currentGroups = uiState.uiState.dataOrNull?.allGroups ?: emptyList() // Data from uiState.uiState.data

        AlertDialog(
            onDismissRequest = { showAssignGroupDialog = false; sessionToAssign = null },
            title = { Text("Assign Session to Group") },
            text = {
                Column {
                    Text("Select a group for '${sessionToAssign?.name}':")
                    Spacer(Modifier.height(8.dp))
                    // Option for "Ungrouped"
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable {
                                sessionToAssign?.let { actions.onAssignSessionToGroup(it.id, null) } // Use actions
                                showAssignGroupDialog = false
                                sessionToAssign = null
                            }
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = sessionToAssign?.groupId == null,
                            onClick = null // Handled by row click
                        )
                        Text("Ungrouped")
                    }
                    // Options for existing groups
                    currentGroups.forEach { group ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    sessionToAssign?.let { actions.onAssignSessionToGroup(it.id, group.id) } // Use actions
                                    showAssignGroupDialog = false
                                    sessionToAssign = null
                                }
                                .padding(vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = sessionToAssign?.groupId == group.id,
                                onClick = null // Handled by row click
                            )
                            Text(group.name)
                        }
                    }
                }
            },
            confirmButton = {
                // No explicit confirm button needed, selection triggers action
            },
            dismissButton = {
                TextButton(onClick = { showAssignGroupDialog = false; sessionToAssign = null }) { Text("Cancel") }
            }
        )
    }

    // Delete Group Confirmation Dialog (E6.S6)
    if (showDeleteGroupDialog && groupToDeleteId != null) {
        AlertDialog(
            onDismissRequest = { showDeleteGroupDialog = false; groupToDeleteId = null },
            title = { Text("Delete Group?") },
            text = { Text("Are you sure you want to delete this group? Any sessions assigned to it will become 'Ungrouped'. This action cannot be undone.") },
            confirmButton = {
                Button(
                    onClick = {
                        groupToDeleteId?.let { actions.onDeleteGroup(it) } // Use actions
                        showDeleteGroupDialog = false
                        groupToDeleteId = null
                    },
                    colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.error)
                ) { Text("Delete") }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteGroupDialog = false; groupToDeleteId = null }) { Text("Cancel") }
            }
        )
    }
}

// SessionListItem and GroupHeader have also been adjusted to use the direct lambda parameters
// that SessionListPanel now provides. Their changes are minor, mostly renaming parameters.

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun SessionListItem(
    session: ChatSessionSummary,
    isSelected: Boolean,
    onClick: (ChatSessionSummary) -> Unit,
    onRename: (ChatSessionSummary) -> Unit,
    onDelete: (Long) -> Unit,
    onAssignToGroup: (ChatSessionSummary) -> Unit
) {
    var showMenu by remember { mutableStateOf(false) }
    var hovered by remember { mutableStateOf(false) }

    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(
                color = when {
                    isSelected -> MaterialTheme.colorScheme.primaryContainer
                    hovered -> MaterialTheme.colorScheme.surfaceVariant
                    else -> MaterialTheme.colorScheme.surface
                }
            )
            .combinedClickable(
                onClick = { onClick(session) },
                onLongClick = { showMenu = true }
            )
            .onPointerEvent(PointerEventType.Enter) { hovered = true }
            .onPointerEvent(PointerEventType.Exit) { hovered = false },
        shape = RoundedCornerShape(8.dp),
        shadowElevation = 1.dp
    ) {
        Row(
            modifier = Modifier
                .padding(horizontal = 12.dp, vertical = 8.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = session.name,
                style = MaterialTheme.typography.bodyLarge,
                color = if (isSelected) MaterialTheme.colorScheme.onPrimaryContainer else MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.weight(1f)
            )
            // Show menu icon on hover for more actions
            if (hovered || showMenu) { // Show if hovered or menu is open
                Box { // Wrap in Box for DropdownMenu positioning
                    IconButton(
                        onClick = { showMenu = true },
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            Icons.Default.MoreVert,
                            contentDescription = "More actions for session ${session.name}",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    DropdownMenu(
                        expanded = showMenu,
                        onDismissRequest = { showMenu = false }
                    ) {
                        DropdownMenuItem(
                            text = { Text("Rename") },
                            onClick = {
                                onRename(session)
                                showMenu = false
                            },
                            leadingIcon = { Icon(Icons.Default.Edit, contentDescription = null) }
                        )
                        DropdownMenuItem(
                            text = { Text("Assign to Group") },
                            onClick = {
                                onAssignToGroup(session)
                                showMenu = false
                            },
                            leadingIcon = { Icon(Icons.Default.FolderOpen, contentDescription = null) }
                        )
                        Divider()
                        DropdownMenuItem(
                            text = { Text("Delete") },
                            onClick = {
                                onDelete(session.id)
                                showMenu = false
                            },
                            leadingIcon = { Icon(Icons.Default.Delete, contentDescription = null) },
                            colors = MenuDefaults.itemColors(
                                textColor = MaterialTheme.colorScheme.error,
                                leadingIconColor = MaterialTheme.colorScheme.error
                            )
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun GroupHeader(
    group: ChatGroup?,
    isEditing: Boolean,
    editingName: String,
    onEditNameChange: (String) -> Unit,
    onSaveRename: () -> Unit,
    onCancelRename: () -> Unit,
    onStartRename: (ChatGroup) -> Unit,
    onDelete: (ChatGroup?) -> Unit
) {
    var showMenu by remember { mutableStateOf(false) }
    var hovered by remember { mutableStateOf(false) }

    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 8.dp, bottom = 4.dp)
            .background(MaterialTheme.colorScheme.surfaceVariant)
            .onPointerEvent(PointerEventType.Enter) { hovered = true }
            .onPointerEvent(PointerEventType.Exit) { hovered = false }
            .combinedClickable(
                onClick = { /* Not directly clickable, actions are via menu/buttons */ },
                onLongClick = { if (group != null) showMenu = true } // Long click only for actual groups
            ),
        shape = RoundedCornerShape(8.dp),
        shadowElevation = 2.dp
    ) {
        Row(
            modifier = Modifier
                .padding(horizontal = 12.dp, vertical = 8.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (isEditing) {
                OutlinedTextField(
                    value = editingName,
                    onValueChange = onEditNameChange,
                    singleLine = true,
                    modifier = Modifier.weight(1f),
                    textStyle = MaterialTheme.typography.titleMedium,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedContainerColor = MaterialTheme.colorScheme.surfaceVariant,
                        unfocusedContainerColor = MaterialTheme.colorScheme.surfaceVariant,
                        disabledContainerColor = MaterialTheme.colorScheme.surfaceVariant,
                        focusedBorderColor = MaterialTheme.colorScheme.primary,
                        unfocusedBorderColor = Color.Transparent,
                        disabledBorderColor = Color.Transparent
                    )
                )
                Spacer(Modifier.width(8.dp))
                IconButton(onClick = onSaveRename, enabled = editingName.isNotBlank()) {
                    Icon(Icons.Default.Check, contentDescription = "Save Group Name")
                }
                IconButton(onClick = onCancelRename) {
                    Icon(Icons.Default.Close, contentDescription = "Cancel Rename")
                }
            } else {
                Text(
                    text = group?.name ?: "Ungrouped",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.weight(1f)
                )
                // Show menu icon on hover for more actions (only for actual groups)
                if (group != null && (hovered || showMenu)) {
                    Box {
                        IconButton(
                            onClick = { showMenu = true },
                            modifier = Modifier.size(24.dp)
                        ) {
                            Icon(
                                Icons.Default.MoreVert,
                                contentDescription = "More actions for group ${group.name}",
                                tint = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        DropdownMenu(
                            expanded = showMenu,
                            onDismissRequest = { showMenu = false }
                        ) {
                            DropdownMenuItem(
                                text = { Text("Rename Group") },
                                onClick = {
                                    onStartRename(group)
                                    showMenu = false
                                },
                                leadingIcon = { Icon(Icons.Default.Edit, contentDescription = null) }
                            )
                            Divider()
                            DropdownMenuItem(
                                text = { Text("Delete Group") },
                                onClick = {
                                    onDelete(group)
                                    showMenu = false
                                },
                                leadingIcon = { Icon(Icons.Default.Delete, contentDescription = null) },
                                colors = MenuDefaults.itemColors(
                                    textColor = MaterialTheme.colorScheme.error,
                                    leadingIconColor = MaterialTheme.colorScheme.error
                                )
                            )
                        }
                    }
                }
            }
        }
    }
}
```

---

This revised implementation provides a clean separation of concerns, with `ChatScreen` acting as the stateful orchestrator and `ChatScreenContent` (and its children like `SessionListPanel`) as truly stateless UI components. This is a solid foundation for building out the rest of the UI.